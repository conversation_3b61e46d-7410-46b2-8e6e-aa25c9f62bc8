'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { FiArrowRight } from 'react-icons/fi';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useTheme } from '@/hooks/useTheme';
import * as LucideIcons from 'lucide-react';
import { useState, useCallback } from 'react';
import { useLoading } from '@/contexts/LoadingContext';
import { Progress } from '@/components/ui/nprogress';

interface UnifiedCardProps {
  // Core data
  id: string;
  title: string;
  description: string;
  icon: string;

  // Type and routing - updated to support unified routing
  type?: 'tool' | 'calculator' | 'blog';
  category?: string;

  // Optional metadata
  inputFormat?: string;
  outputFormat?: string;
  popular?: boolean;
  comingSoon?: boolean;

  // Blog-specific
  author?: string;
  date?: string;
  image?: string;

  // Animation
  index?: number;
  delay?: number;

  // Styling
  className?: string;
  variant?: 'default' | 'enhanced' | 'minimal';
}

const UnifiedCard = React.memo(function UnifiedCard({
  id,
  title,
  description,
  icon,
  type = 'tool',
  category,
  inputFormat,
  outputFormat,
  popular = false,
  comingSoon = false,
  author,
  date,
  image,
  index = 0,
  delay,
  className,
  variant = 'enhanced'
}: UnifiedCardProps) {
  const router = useRouter();
  const { theme } = useTheme();
  const { startLoading } = useLoading();
  const [isNavigating, setIsNavigating] = useState(false);

  const isDark = theme === 'dark';

  // Calculate animation delay
  const animationDelay = delay ?? 0.05 * (index % 8);

  // Generate the correct path using unified routing
  const getUnifiedHref = () => {
    switch (type) {
      case 'calculator':
        return `/calculators/${id}`;
      case 'tool':
        return `/tools/${id}`;
      case 'blog':
        return `/blogs/${id}`;
      default:
        return `/tools/${id}`;
    }
  };

  const href = getUnifiedHref();

  // Handle click with improved navigation and loading indicators
  const handleClick = useCallback((e: React.MouseEvent) => {
    if (comingSoon) {
      e.preventDefault();
      return;
    }

    // Start loading indicators
    setIsNavigating(true);
    startLoading();
    Progress.start();

    // Reset loading state after navigation attempt
    const timeoutId = setTimeout(() => {
      setIsNavigating(false);
    }, 2000);

    // Clean up timeout if component unmounts
    return () => clearTimeout(timeoutId);
  }, [comingSoon, startLoading]);

  // Icon rendering for all content types
  const renderIcon = (iconName: string, className: string = "w-8 h-8") => {
    if (type === 'tool') {
      return <span className="text-4xl">{iconName}</span>;
    }

    if (type === 'blog') {
      // For blogs, show image if available, otherwise use a default icon
      if (image) {
        return (
          <div className="w-12 h-12 rounded-lg overflow-hidden">
            <img
              src={image}
              alt={title}
              className="w-full h-full object-cover"
            />
          </div>
        );
      }
      return <LucideIcons.FileText className={className} />;
    }

    // For calculators, use Lucide icons
    const pascalCaseName = iconName
      .split("-")
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join("");

    const IconComponent = (LucideIcons as any)[pascalCaseName];

    if (IconComponent) {
      return <IconComponent className={className} />;
    }

    return <LucideIcons.Calculator className={className} />;
  };

  // Enhanced category colors with neon accents for dark mode
  const getCategoryColor = () => {
    if (type === 'calculator') {
      const categoryColors = {
        finance: isDark
          ? 'bg-gradient-to-r from-green-900/40 to-emerald-900/40 text-green-300 border border-green-700/50'
          : 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200',
        math: isDark
          ? 'bg-gradient-to-r from-blue-900/40 to-indigo-900/40 text-blue-300 border border-blue-700/50'
          : 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200',
        conversion: isDark
          ? 'bg-gradient-to-r from-purple-900/40 to-pink-900/40 text-purple-300 border border-purple-700/50'
          : 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border border-purple-200',
        health: isDark
          ? 'bg-gradient-to-r from-red-900/40 to-rose-900/40 text-red-300 border border-red-700/50'
          : 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200',
        developer: isDark
          ? 'bg-gradient-to-r from-cyan-900/40 to-teal-900/40 text-cyan-300 border border-cyan-700/50'
          : 'bg-gradient-to-r from-cyan-100 to-teal-100 text-cyan-800 border border-cyan-200',
        education: isDark
          ? 'bg-gradient-to-r from-amber-900/40 to-yellow-900/40 text-amber-300 border border-amber-700/50'
          : 'bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 border border-amber-200',
        lifestyle: isDark
          ? 'bg-gradient-to-r from-orange-900/40 to-red-900/40 text-orange-300 border border-orange-700/50'
          : 'bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 border border-orange-200',
        business: isDark
          ? 'bg-gradient-to-r from-slate-900/40 to-gray-900/40 text-slate-300 border border-slate-700/50'
          : 'bg-gradient-to-r from-slate-100 to-gray-100 text-slate-800 border border-slate-200',
      };
      return categoryColors[category as keyof typeof categoryColors] || (isDark
        ? 'bg-gray-800/50 text-gray-300 border border-gray-700/50'
        : 'bg-gray-100 text-gray-800 border border-gray-200');
    }

    if (type === 'blog') {
      const blogColors = {
        'Tips & Tricks': isDark
          ? 'bg-gradient-to-r from-emerald-900/40 to-green-900/40 text-emerald-300 border border-emerald-700/50'
          : 'bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border border-emerald-200',
        'Conversion': isDark
          ? 'bg-gradient-to-r from-blue-900/40 to-indigo-900/40 text-blue-300 border border-blue-700/50'
          : 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200',
        'Security': isDark
          ? 'bg-gradient-to-r from-red-900/40 to-rose-900/40 text-red-300 border border-red-700/50'
          : 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200',
        'Accessibility': isDark
          ? 'bg-gradient-to-r from-purple-900/40 to-pink-900/40 text-purple-300 border border-purple-700/50'
          : 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border border-purple-200',
        'Comparison': isDark
          ? 'bg-gradient-to-r from-orange-900/40 to-amber-900/40 text-orange-300 border border-orange-700/50'
          : 'bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 border border-orange-200',
        'Optimization': isDark
          ? 'bg-gradient-to-r from-indigo-900/40 to-violet-900/40 text-indigo-300 border border-indigo-700/50'
          : 'bg-gradient-to-r from-indigo-100 to-violet-100 text-indigo-800 border border-indigo-200',
      };
      return blogColors[category as keyof typeof blogColors] || (isDark
        ? 'bg-gray-800/50 text-gray-300 border border-gray-700/50'
        : 'bg-gray-100 text-gray-800 border border-gray-200');
    }

    // Enhanced tool colors
    const toolColors = {
      pdf: isDark
        ? 'bg-gradient-to-r from-blue-900/40 to-indigo-900/40 text-blue-300 border border-blue-700/50'
        : 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border border-blue-200',
      office: isDark
        ? 'bg-gradient-to-r from-purple-900/40 to-violet-900/40 text-purple-300 border border-purple-700/50'
        : 'bg-gradient-to-r from-purple-100 to-violet-100 text-purple-800 border border-purple-200',
      image: isDark
        ? 'bg-gradient-to-r from-green-900/40 to-emerald-900/40 text-green-300 border border-green-700/50'
        : 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200',
      web: isDark
        ? 'bg-gradient-to-r from-orange-900/40 to-amber-900/40 text-orange-300 border border-orange-700/50'
        : 'bg-gradient-to-r from-orange-100 to-amber-100 text-orange-800 border border-orange-200',
    };
    return toolColors[category as keyof typeof toolColors] || (isDark
      ? 'bg-gray-800/50 text-gray-300 border border-gray-700/50'
      : 'bg-gray-100 text-gray-800 border border-gray-200');
  };

  // Card content
  const cardContent = (
    <div className="flex flex-col h-full">
      {/* Header with icon and category */}
      <div className="flex items-start justify-between mb-4">
        <motion.div
          className="relative"
          whileHover={{
            scale: type === 'calculator' ? 1.2 : type === 'blog' ? 1.05 : 1.1,
            rotate: type === 'calculator' ? 10 : type === 'blog' ? 2 : 5
          }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          {renderIcon(icon)}

          {/* Enhanced glow effect for all types */}
          <div className={`absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md -z-10 ${
            type === 'calculator'
              ? 'bg-yellow-400/30'
              : type === 'blog'
              ? 'bg-green-400/30'
              : 'bg-blue-400/20'
          }`} />
        </motion.div>

        <div className="flex flex-col gap-2">
          {category && (
            <motion.span
              whileHover={{ scale: 1.05 }}
              className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 ${getCategoryColor()}`}
            >
              {category}
            </motion.span>
          )}

          {popular && (
            <motion.span
              whileHover={{ scale: 1.05 }}
              className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 ${
                isDark
                  ? 'bg-gradient-to-r from-yellow-900/50 to-amber-900/50 text-yellow-300 border border-yellow-700/50'
                  : 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 border border-yellow-200'
              }`}
            >
              ⭐ Popular
            </motion.span>
          )}
        </div>
      </div>

      {/* Title and description */}
      <h3 className="text-xl font-semibold mb-3 group-hover:text-blue-500 transition-colors flex-shrink-0"
        style={{ color: 'var(--text-primary)' }}
      >
        {title}
      </h3>

      <p className="mb-4 flex-grow" style={{ color: 'var(--text-secondary)' }}>
        {description}
      </p>

      {/* Footer */}
      <div className="flex justify-between items-center mt-auto">
        {/* Tool format info */}
        {type === 'tool' && inputFormat && outputFormat && (
          <div className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
            {inputFormat} → {outputFormat}
          </div>
        )}

        {/* Blog metadata */}
        {type === 'blog' && (author || date) && (
          <div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
            {author && (
              <span className="flex items-center gap-1">
                <LucideIcons.User className="w-3 h-3" />
                {author}
              </span>
            )}
            {date && (
              <span className="flex items-center gap-1">
                <LucideIcons.Calendar className="w-3 h-3" />
                {date}
              </span>
            )}
          </div>
        )}

        <motion.div
          whileHover={{ x: 4, scale: 1.1 }}
          transition={{ duration: 0.2 }}
          className="ml-auto"
        >
          <FiArrowRight className={`w-5 h-5 transition-all duration-300 ${
            type === 'calculator'
              ? 'text-yellow-500 group-hover:text-yellow-600 dark:text-yellow-400 dark:group-hover:text-yellow-300'
              : type === 'blog'
              ? 'text-green-500 group-hover:text-green-600 dark:text-green-400 dark:group-hover:text-green-300'
              : 'text-gray-400 group-hover:text-blue-500 dark:group-hover:text-blue-400'
          }`} />
        </motion.div>
      </div>
    </div>
  );

  // Coming soon overlay
  const comingSoonOverlay = comingSoon && (
    <div className="absolute inset-0 flex items-center justify-center backdrop-blur-[2px] z-10 rounded-xl"
      style={{
        backgroundColor: isDark ? 'rgba(17, 24, 39, 0.8)' : 'rgba(255, 255, 255, 0.8)'
      }}
    >
      <motion.span
        whileHover={{ scale: 1.05 }}
        className="px-4 py-1.5 bg-blue-600 text-white rounded-full text-sm font-medium shadow-md"
      >
        Coming Soon
      </motion.span>
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.3, delay: animationDelay }}
      whileHover={{
        y: type === 'blog' ? -8 : type === 'tool' ? -6 : type === 'calculator' ? -4 : -5,
        scale: type === 'blog' ? 1.02 : type === 'tool' ? 1.02 : type === 'calculator' ? 1.01 : 1.02,
        boxShadow: type === 'blog' ? '0 20px 40px rgba(0, 0, 0, 0.15)' :
                   type === 'tool' ? '0 15px 30px rgba(0, 0, 0, 0.12)' :
                   type === 'calculator' ? '0 12px 25px rgba(0, 0, 0, 0.1)' :
                   '0 10px 20px rgba(0, 0, 0, 0.1)',
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      className={cn("h-full", className)}
    >
      {comingSoon ? (
        <Card
          className={cn(
            "group relative h-full p-6 rounded-xl border transition-all duration-300 overflow-hidden shadow-sm hover:shadow-lg cursor-not-allowed",
            // Enhanced backgrounds for different types
            variant === 'enhanced' && type === 'calculator' && !isDark && "bg-gradient-to-br from-yellow-50 via-white to-orange-50",
            variant === 'enhanced' && type === 'calculator' && isDark && "bg-gradient-to-br from-gray-900/80 via-gray-800/80 to-yellow-900/20",
            variant === 'enhanced' && type === 'tool' && !isDark && "bg-gradient-to-br from-blue-50 via-white to-purple-50",
            variant === 'enhanced' && type === 'tool' && isDark && "bg-gradient-to-br from-gray-900/80 via-gray-800/80 to-blue-900/20",
            variant === 'enhanced' && type === 'blog' && !isDark && "bg-gradient-to-br from-green-50 via-white to-emerald-50",
            variant === 'enhanced' && type === 'blog' && isDark && "bg-gradient-to-br from-gray-900/80 via-gray-800/80 to-green-900/20",
            isNavigating && "opacity-75"
          )}
          style={{
            borderColor: isDark
              ? (type === 'calculator' ? '#fbbf24' : type === 'tool' ? '#3b82f6' : '#10b981') + '20'
              : '#e5e7eb',
            color: 'var(--text-primary)'
          }}
        >
          {cardContent}
          {comingSoonOverlay}
        </Card>
      ) : (
        <Link href={href} onClick={handleClick} className="block h-full">
          <Card
            className={cn(
              "group relative h-full p-6 rounded-xl border transition-all duration-300 overflow-hidden shadow-sm hover:shadow-xl cursor-pointer",
              // Enhanced backgrounds for different types
              variant === 'enhanced' && type === 'calculator' && !isDark && "bg-gradient-to-br from-yellow-50 via-white to-orange-50",
              variant === 'enhanced' && type === 'calculator' && isDark && "bg-gradient-to-br from-gray-900/80 via-gray-800/80 to-yellow-900/20",
              variant === 'enhanced' && type === 'tool' && !isDark && "bg-gradient-to-br from-blue-50 via-white to-purple-50",
              variant === 'enhanced' && type === 'tool' && isDark && "bg-gradient-to-br from-gray-900/80 via-gray-800/80 to-blue-900/20",
              variant === 'enhanced' && type === 'blog' && !isDark && "bg-gradient-to-br from-green-50 via-white to-emerald-50",
              variant === 'enhanced' && type === 'blog' && isDark && "bg-gradient-to-br from-gray-900/80 via-gray-800/80 to-green-900/20",
              isNavigating && "opacity-75"
            )}
            style={{
              borderColor: isDark
                ? (type === 'calculator' ? '#fbbf24' : type === 'tool' ? '#3b82f6' : '#10b981') + '20'
                : '#e5e7eb',
              color: 'var(--text-primary)'
            }}
          >
            {cardContent}

            {/* Enhanced gradient background effect on hover */}
            <div className="absolute inset-0 -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{
                background: isDark
                  ? type === 'calculator'
                    ? 'linear-gradient(to bottom right, rgba(251, 191, 36, 0.1), rgba(31, 41, 55, 0.8))'
                    : type === 'tool'
                    ? 'linear-gradient(to bottom right, rgba(59, 130, 246, 0.1), rgba(31, 41, 55, 0.8))'
                    : 'linear-gradient(to bottom right, rgba(16, 185, 129, 0.1), rgba(31, 41, 55, 0.8))'
                  : type === 'calculator'
                  ? 'linear-gradient(to bottom right, rgba(254, 240, 138, 0.6), rgba(255, 255, 255, 0.8))'
                  : type === 'tool'
                  ? 'linear-gradient(to bottom right, rgba(219, 234, 254, 0.6), rgba(255, 255, 255, 0.8))'
                  : 'linear-gradient(to bottom right, rgba(209, 250, 229, 0.6), rgba(255, 255, 255, 0.8))'
              }}
            />

            {/* Enhanced border glow effect on hover */}
            <div className="absolute inset-0 -z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-xl rounded-xl"
              style={{
                backgroundColor: isDark
                  ? type === 'calculator'
                    ? 'rgba(251, 191, 36, 0.15)'
                    : type === 'tool'
                    ? 'rgba(59, 130, 246, 0.15)'
                    : 'rgba(16, 185, 129, 0.15)'
                  : type === 'calculator'
                  ? 'rgba(251, 191, 36, 0.1)'
                  : type === 'tool'
                  ? 'rgba(59, 130, 246, 0.1)'
                  : 'rgba(16, 185, 129, 0.1)'
              }}
            />

            {/* Texture overlay for enhanced depth */}
            <div className="absolute inset-0 -z-30 opacity-0 group-hover:opacity-20 transition-opacity duration-300" style={{
              backgroundImage: isDark
                ? `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0)`
                : `radial-gradient(circle at 1px 1px, rgba(0,0,0,0.05) 1px, transparent 0)`,
              backgroundSize: '16px 16px'
            }} />
          </Card>
        </Link>
      )}
    </motion.div>
  );
});

export default UnifiedCard;
