import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import connectToDatabase from "@/lib/db";
import Contact from "@/models/Contact";

// Contact form validation schema
const ContactSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name must be less than 100 characters"),
  email: z.string().email("Please enter a valid email address"),
  category: z.enum(["general", "technical", "bug", "feature", "business"], {
    errorMap: () => ({ message: "Please select a valid category" })
  }),
  message: z.string().min(10, "Message must be at least 10 characters").max(5000, "Message must be less than 5000 characters"),
});

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    console.log("Received contact form data:", body);

    // Validate the data
    const validation = ContactSchema.safeParse(body);
    if (!validation.success) {
      console.log("Validation failed:", validation.error);
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: validation.error.issues.map(issue => ({
            field: issue.path.join('.'),
            message: issue.message
          }))
        },
        { status: 400 }
      );
    }

    const { name, email, category, message } = validation.data;

    // Get client information for logging
    const ipAddress = request.headers.get("x-forwarded-for") || 
                     request.headers.get("x-real-ip") || 
                     "unknown";
    const userAgent = request.headers.get("user-agent") || "unknown";

    // Here you can:
    // 1. Save to database (optional)
    // 2. Send email notification
    // 3. Integrate with support ticket system
    // 4. Send to Slack/Discord webhook

    try {
      // Save to database
      await connectToDatabase();

      // Create new contact record
      const contact = new Contact({
        name,
        email,
        category,
        message,
        ipAddress,
        userAgent,
        status: "new",
        priority: "medium",
        responseSent: false,
      });

      await contact.save();

      console.log("Contact form submission saved:", {
        id: contact._id,
        name,
        email,
        category,
        message: message.substring(0, 100) + "...", // Log first 100 chars
        ipAddress,
        userAgent,
        timestamp: new Date().toISOString()
      });

      // TODO: Implement email sending logic here
      // Example with nodemailer or your preferred email service:
      /*
      await sendEmail({
        to: "<EMAIL>",
        subject: `New Contact Form: ${category}`,
        html: `
          <h2>New Contact Form Submission</h2>
          <p><strong>Name:</strong> ${name}</p>
          <p><strong>Email:</strong> ${email}</p>
          <p><strong>Category:</strong> ${category}</p>
          <p><strong>Message:</strong></p>
          <p>${message}</p>
          <hr>
          <p><small>IP: ${ipAddress} | User Agent: ${userAgent}</small></p>
        `
      });

      // Send auto-reply to user
      await sendEmail({
        to: email,
        subject: "We received your message - ToolCrush Support",
        html: `
          <h2>Thank you for contacting us!</h2>
          <p>Hi ${name},</p>
          <p>We've received your message and will get back to you within 24 hours.</p>
          <p><strong>Your message:</strong></p>
          <p>${message}</p>
          <br>
          <p>Best regards,<br>ToolCrush Support Team</p>
        `
      });
      */

      return NextResponse.json(
        { 
          success: true, 
          message: "Thank you for your message! We'll get back to you soon." 
        },
        { status: 200 }
      );

    } catch (dbError) {
      console.error("Database/Email error:", dbError);
      // Still return success to user, but log the error
      return NextResponse.json(
        { 
          success: true, 
          message: "Thank you for your message! We'll get back to you soon." 
        },
        { status: 200 }
      );
    }

  } catch (error) {
    console.error("Contact form error:", error);
    return NextResponse.json(
      { error: "Internal server error. Please try again later." },
      { status: 500 }
    );
  }
}

// GET method to retrieve contact messages (admin only)
export async function GET(request: NextRequest) {
  try {
    // Check if user is admin (implement your auth check here)
    const userRole = request.headers.get("x-user-role");
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    await connectToDatabase();
    
    // TODO: Implement database query to get contact messages
    // const messages = await ContactMessage.find().sort({ createdAt: -1 });
    
    return NextResponse.json(
      { 
        success: true, 
        messages: [], // Return actual messages from database
        total: 0 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error("Error fetching contact messages:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
