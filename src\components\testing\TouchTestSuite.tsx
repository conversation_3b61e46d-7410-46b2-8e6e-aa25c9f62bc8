'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TouchableCard } from '@/components/ui/TouchableCard';
import { TouchableButton } from '@/components/ui/TouchableButton';
import { TouchableCalculatorButton } from '@/components/calculators/TouchableCalculatorButton';
import { useTouch, hapticFeedback } from '@/hooks/useTouch';
import { getPlatformInfo } from '@/utils/platform';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, Smartphone, Tablet, Monitor } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  timestamp: Date;
}

export function TouchTestSuite() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [platformInfo, setPlatformInfo] = useState(getPlatformInfo());
  const [touchEvents, setTouchEvents] = useState<string[]>([]);

  useEffect(() => {
    setPlatformInfo(getPlatformInfo());
  }, []);

  const addTestResult = (name: string, status: 'pass' | 'fail' | 'warning', message: string) => {
    setTestResults(prev => [...prev, {
      name,
      status,
      message,
      timestamp: new Date()
    }]);
  };

  const logTouchEvent = (event: string) => {
    setTouchEvents(prev => [...prev.slice(-9), event]);
  };

  const runTouchTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    // Test 1: Platform Detection
    addTestResult(
      'Platform Detection',
      platformInfo.isTouchDevice ? 'pass' : 'warning',
      `Device: ${platformInfo.isMobile ? 'Mobile' : platformInfo.isTablet ? 'Tablet' : 'Desktop'}, Touch: ${platformInfo.isTouchDevice ? 'Yes' : 'No'}`
    );

    // Test 2: Touch Target Size
    const touchTargets = document.querySelectorAll('.touch-target, .touch-button, .touch-card');
    let minSizePass = true;
    touchTargets.forEach(target => {
      const rect = target.getBoundingClientRect();
      if (rect.width < 44 || rect.height < 44) {
        minSizePass = false;
      }
    });
    addTestResult(
      'Touch Target Size',
      minSizePass ? 'pass' : 'fail',
      `${touchTargets.length} touch targets checked. Min size: ${minSizePass ? '✓ 44px+' : '✗ <44px'}`
    );

    // Test 3: Haptic Feedback
    addTestResult(
      'Haptic Feedback',
      platformInfo.supportsVibration ? 'pass' : 'warning',
      `Vibration API: ${platformInfo.supportsVibration ? 'Supported' : 'Not supported'}`
    );

    // Test 4: Web Share API
    addTestResult(
      'Web Share API',
      platformInfo.supportsShare ? 'pass' : 'warning',
      `Share API: ${platformInfo.supportsShare ? 'Supported' : 'Not supported'}`
    );

    // Test 5: CSS Touch Support
    const hasHoverMedia = window.matchMedia('(hover: hover)').matches;
    const hasPointerMedia = window.matchMedia('(pointer: coarse)').matches;
    addTestResult(
      'CSS Touch Media Queries',
      'pass',
      `Hover: ${hasHoverMedia ? 'Yes' : 'No'}, Coarse pointer: ${hasPointerMedia ? 'Yes' : 'No'}`
    );

    // Test 6: Performance
    const startTime = performance.now();
    await new Promise(resolve => setTimeout(resolve, 100));
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    addTestResult(
      'Touch Response Time',
      responseTime < 100 ? 'pass' : 'warning',
      `Response time: ${responseTime.toFixed(2)}ms (target: <100ms)`
    );

    // Test 7: Accessibility
    const focusableElements = document.querySelectorAll('[tabindex], button, input, select, textarea, a[href]');
    addTestResult(
      'Accessibility',
      focusableElements.length > 0 ? 'pass' : 'warning',
      `${focusableElements.length} focusable elements found`
    );

    setIsRunning(false);
  };

  const testTouchHandlers = {
    onTap: () => logTouchEvent('Tap'),
    onDoubleTap: () => logTouchEvent('Double Tap'),
    onLongPress: () => logTouchEvent('Long Press'),
    onSwipeLeft: () => logTouchEvent('Swipe Left'),
    onSwipeRight: () => logTouchEvent('Swipe Right'),
    onSwipeUp: () => logTouchEvent('Swipe Up'),
    onSwipeDown: () => logTouchEvent('Swipe Down'),
  };

  const getStatusIcon = (status: 'pass' | 'fail' | 'warning') => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'fail':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getPlatformIcon = () => {
    if (platformInfo.isMobile) return <Smartphone className="w-5 h-5" />;
    if (platformInfo.isTablet) return <Tablet className="w-5 h-5" />;
    return <Monitor className="w-5 h-5" />;
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Touch Functionality Test Suite</h1>
        <p className="text-muted-foreground mb-6">
          Comprehensive testing for mobile and tablet touch interactions
        </p>
      </div>

      {/* Platform Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getPlatformIcon()}
            Platform Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Device Type</p>
              <Badge variant={platformInfo.isTouchDevice ? 'default' : 'secondary'}>
                {platformInfo.isMobile ? 'Mobile' : platformInfo.isTablet ? 'Tablet' : 'Desktop'}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Touch Support</p>
              <Badge variant={platformInfo.isTouchDevice ? 'default' : 'secondary'}>
                {platformInfo.isTouchDevice ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Hover Support</p>
              <Badge variant={platformInfo.hasHover ? 'default' : 'secondary'}>
                {platformInfo.hasHover ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">OS</p>
              <Badge variant="outline">{platformInfo.osVersion}</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <TouchableButton
            onTap={runTouchTests}
            disabled={isRunning}
            variant="primary"
            size="lg"
            className="w-full"
          >
            {isRunning ? 'Running Tests...' : 'Run Touch Tests'}
          </TouchableButton>
        </CardContent>
      </Card>

      {/* Interactive Test Area */}
      <Card>
        <CardHeader>
          <CardTitle>Interactive Touch Tests</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <TouchableCard
              {...testTouchHandlers}
              className="h-32 flex items-center justify-center"
              variant="elevated"
            >
              <div className="text-center">
                <p className="font-semibold">Touch Card</p>
                <p className="text-sm text-muted-foreground">Try all gestures</p>
              </div>
            </TouchableCard>

            <TouchableButton
              onTap={() => logTouchEvent('Button Tap')}
              onLongPress={() => logTouchEvent('Button Long Press')}
              variant="primary"
              size="lg"
              className="h-32"
            >
              Touch Button
            </TouchableButton>

            <TouchableCalculatorButton
              onClick={() => logTouchEvent('Calculator Button')}
              onLongPress={() => logTouchEvent('Calculator Long Press')}
              variant="number"
              size="lg"
            >
              7
            </TouchableCalculatorButton>
          </div>

          {/* Touch Event Log */}
          <div>
            <h4 className="font-semibold mb-2">Touch Event Log</h4>
            <div className="bg-muted p-3 rounded-md min-h-[100px]">
              {touchEvents.length === 0 ? (
                <p className="text-muted-foreground text-sm">No touch events yet. Try interacting with the elements above.</p>
              ) : (
                <div className="space-y-1">
                  {touchEvents.map((event, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="text-sm font-mono"
                    >
                      {event}
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center gap-3 p-3 rounded-md border"
                >
                  {getStatusIcon(result.status)}
                  <div className="flex-1">
                    <p className="font-semibold">{result.name}</p>
                    <p className="text-sm text-muted-foreground">{result.message}</p>
                  </div>
                  <Badge variant={result.status === 'pass' ? 'default' : result.status === 'fail' ? 'destructive' : 'secondary'}>
                    {result.status}
                  </Badge>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default TouchTestSuite;
