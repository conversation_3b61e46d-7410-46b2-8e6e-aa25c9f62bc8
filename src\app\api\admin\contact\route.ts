import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { z } from "zod";
import connectToDatabase from "@/lib/db";
import Contact from "@/models/Contact";
import User from "@/models/User";

const secret = process.env.NEXTAUTH_SECRET;

// Validation schemas
const ContactQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("10"),
  search: z.string().optional(),
  status: z.enum(["new", "read", "in-progress", "resolved", "all"]).optional().default("all"),
  category: z.enum(["general", "technical", "bug", "feature", "business", "all"]).optional().default("all"),
  priority: z.enum(["low", "medium", "high", "all"]).optional().default("all"),
  assignedTo: z.string().optional(),
  dateRange: z.enum(["7", "30", "90", "all"]).optional().default("all"),
  sortBy: z.enum(["createdAt", "updatedAt", "status", "priority", "name", "email", "category"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

// Helper function to check admin authorization
async function checkAdminAuth(request: NextRequest) {
  const token = await getToken({ req: request, secret });
  
  if (!token || token.role !== "admin") {
    return NextResponse.json(
      { error: "Unauthorized. Admin access required." },
      { status: 401 }
    );
  }
  
  return null;
}

// GET /api/admin/contact - List contacts with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    await connectToDatabase();

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validation = ContactQuerySchema.safeParse(queryParams);
    if (!validation.success) {
      console.error("Contact query validation failed:", validation.error.issues);
      console.error("Query params received:", queryParams);
      return NextResponse.json(
        { error: "Invalid query parameters", details: validation.error.issues },
        { status: 400 }
      );
    }

    const {
      page,
      limit,
      search,
      status,
      category,
      priority,
      assignedTo,
      dateRange,
      sortBy,
      sortOrder,
    } = validation.data;

    // Build filter query
    const filter: any = { deletedAt: { $exists: false } };

    // Status filter
    if (status !== "all") {
      filter.status = status;
    }

    // Category filter
    if (category !== "all") {
      filter.category = category;
    }

    // Priority filter
    if (priority !== "all") {
      filter.priority = priority;
    }

    // Assigned to filter
    if (assignedTo && assignedTo !== "all") {
      if (assignedTo === "unassigned") {
        filter.assignedTo = { $exists: false };
      } else {
        filter.assignedTo = assignedTo;
      }
    }

    // Date range filter
    if (dateRange !== "all") {
      const days = parseInt(dateRange);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);
      filter.createdAt = { $gte: startDate };
    }

    // Search filter
    if (search && search.trim()) {
      filter.$text = { $search: search.trim() };
    }

    // Pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Sort configuration
    const sortConfig: any = {};
    sortConfig[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Execute queries
    const [contacts, totalCount, stats] = await Promise.all([
      Contact.find(filter)
        .populate("assignedTo", "name email")
        .sort(sortConfig)
        .skip(skip)
        .limit(limitNum)
        .lean(),
      Contact.countDocuments(filter),
      Contact.getStats(),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limitNum);
    const hasNextPage = pageNum < totalPages;
    const hasPrevPage = pageNum > 1;

    return NextResponse.json({
      success: true,
      data: {
        contacts,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalCount,
          limit: limitNum,
          hasNextPage,
          hasPrevPage,
        },
        stats,
        filters: {
          status,
          category,
          priority,
          assignedTo,
          dateRange,
          search,
        },
      },
    });

  } catch (error) {
    console.error("Error fetching contacts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/contact - Create a new contact (admin only)
export async function POST(request: NextRequest) {
  try {
    // Check admin authorization
    const authError = await checkAdminAuth(request);
    if (authError) return authError;

    await connectToDatabase();

    const body = await request.json();
    
    // Validate contact data
    const ContactCreateSchema = z.object({
      name: z.string().min(2).max(100),
      email: z.string().email(),
      category: z.enum(["general", "technical", "bug", "feature", "business"]),
      message: z.string().min(10).max(5000),
      priority: z.enum(["low", "medium", "high"]).optional().default("medium"),
      assignedTo: z.string().optional(),
      internalNotes: z.string().max(2000).optional(),
    });

    const validation = ContactCreateSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validation.error.issues },
        { status: 400 }
      );
    }

    const contact = new Contact(validation.data);
    await contact.save();

    // Populate assigned user if exists
    await contact.populate("assignedTo", "name email");

    return NextResponse.json({
      success: true,
      data: contact,
      message: "Contact created successfully",
    });

  } catch (error) {
    console.error("Error creating contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
