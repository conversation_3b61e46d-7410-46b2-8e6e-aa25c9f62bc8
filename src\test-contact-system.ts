// test-contact-system.ts - Test script to verify contact management system

/**
 * This is a test script to verify the contact management system functionality.
 * Run this after starting the development server to test all features.
 */

interface TestResult {
  test: string;
  passed: boolean;
  error?: string;
}

class ContactSystemTester {
  private baseUrl = 'http://localhost:3000';
  private results: TestResult[] = [];

  async runAllTests(): Promise<void> {
    console.log('🧪 Starting Contact Management System Tests...\n');

    // Test 1: Contact form submission
    await this.testContactFormSubmission();

    // Test 2: Admin contact list API
    await this.testAdminContactListAPI();

    // Test 3: Contact update API
    await this.testContactUpdateAPI();

    // Test 4: Bulk operations API
    await this.testBulkOperationsAPI();

    // Test 5: CSV export API
    await this.testCSVExportAPI();

    // Test 6: Contact deletion API
    await this.testContactDeletionAPI();

    // Print results
    this.printResults();
  }

  private async testContactFormSubmission(): Promise<void> {
    try {
      const testContact = {
        name: "Test User",
        email: "<EMAIL>",
        category: "technical",
        message: "This is a test message for the contact management system."
      };

      const response = await fetch(`${this.baseUrl}/api/contact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testContact),
      });

      const data = await response.json();

      this.results.push({
        test: 'Contact Form Submission',
        passed: response.ok && data.success,
        error: !response.ok ? `Status: ${response.status}, Error: ${data.error}` : undefined
      });

    } catch (error) {
      this.results.push({
        test: 'Contact Form Submission',
        passed: false,
        error: `Network error: ${error}`
      });
    }
  }

  private async testAdminContactListAPI(): Promise<void> {
    try {
      // Note: This test requires admin authentication
      // In a real test, you would need to authenticate first
      const response = await fetch(`${this.baseUrl}/api/admin/contact?page=1&limit=10`);
      
      // We expect 401 without authentication, which is correct behavior
      this.results.push({
        test: 'Admin Contact List API (Auth Check)',
        passed: response.status === 401,
        error: response.status !== 401 ? `Expected 401, got ${response.status}` : undefined
      });

    } catch (error) {
      this.results.push({
        test: 'Admin Contact List API (Auth Check)',
        passed: false,
        error: `Network error: ${error}`
      });
    }
  }

  private async testContactUpdateAPI(): Promise<void> {
    try {
      // Test with dummy ID - should return 401 without auth
      const response = await fetch(`${this.baseUrl}/api/admin/contact/dummy-id`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'read' }),
      });

      this.results.push({
        test: 'Contact Update API (Auth Check)',
        passed: response.status === 401,
        error: response.status !== 401 ? `Expected 401, got ${response.status}` : undefined
      });

    } catch (error) {
      this.results.push({
        test: 'Contact Update API (Auth Check)',
        passed: false,
        error: `Network error: ${error}`
      });
    }
  }

  private async testBulkOperationsAPI(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/admin/contact/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'updateStatus',
          contactIds: ['dummy-id'],
          data: { status: 'read' }
        }),
      });

      this.results.push({
        test: 'Bulk Operations API (Auth Check)',
        passed: response.status === 401,
        error: response.status !== 401 ? `Expected 401, got ${response.status}` : undefined
      });

    } catch (error) {
      this.results.push({
        test: 'Bulk Operations API (Auth Check)',
        passed: false,
        error: `Network error: ${error}`
      });
    }
  }

  private async testCSVExportAPI(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/admin/contact/bulk/export`);

      this.results.push({
        test: 'CSV Export API (Auth Check)',
        passed: response.status === 401,
        error: response.status !== 401 ? `Expected 401, got ${response.status}` : undefined
      });

    } catch (error) {
      this.results.push({
        test: 'CSV Export API (Auth Check)',
        passed: false,
        error: `Network error: ${error}`
      });
    }
  }

  private async testContactDeletionAPI(): Promise<void> {
    try {
      const response = await fetch(`${this.baseUrl}/api/admin/contact/dummy-id`, {
        method: 'DELETE',
      });

      this.results.push({
        test: 'Contact Deletion API (Auth Check)',
        passed: response.status === 401,
        error: response.status !== 401 ? `Expected 401, got ${response.status}` : undefined
      });

    } catch (error) {
      this.results.push({
        test: 'Contact Deletion API (Auth Check)',
        passed: false,
        error: `Network error: ${error}`
      });
    }
  }

  private printResults(): void {
    console.log('\n📊 Test Results:');
    console.log('================\n');

    let passed = 0;
    let failed = 0;

    this.results.forEach(result => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} - ${result.test}`);
      
      if (!result.passed && result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      result.passed ? passed++ : failed++;
    });

    console.log(`\n📈 Summary: ${passed} passed, ${failed} failed`);
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Contact management system is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please check the errors above.');
    }
  }
}

// Export for use in other files
export default ContactSystemTester;

// If running directly with Node.js
if (typeof window === 'undefined' && require.main === module) {
  const tester = new ContactSystemTester();
  tester.runAllTests().catch(console.error);
}
