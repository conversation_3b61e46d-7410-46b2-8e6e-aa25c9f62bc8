import type { <PERSON>ada<PERSON> } from "next";
import "@/styles.css"; // Using @ alias for consistency
import Script from "next/script";
import { TempoInit } from "./tempo-init";
import { Providers } from "./providers";
import { ThemeInitializer } from "@/components/theme/ThemeInitializer";
import { MetaHead } from "@/components/MetaHead";
import { LoadingIndicator } from "@/components/ui/LoadingIndicator";
import { NavigationProgress } from "@/components/ui/nprogress";
import PlatformInitializer from "@/components/platform/PlatformInitializer";
import connectToDatabase from "@/lib/db";
import SiteSettings from "@/models/SiteSettings";
import { inter } from "@/lib/fonts"; // Import local font configuration

// Dynamic metadata generation
export async function generateMetadata(): Promise<Metadata> {
  try {
    // Connect to the database
    await connectToDatabase();

    // Find the site settings
    const settings = await SiteSettings.findOne({});

    // If no settings exist, return default metadata
    if (!settings) {
      return {
        title: {
          default: "PDF Tools - All-in-one PDF Solution",
          template: "%s | PDF Tools"
        },
        description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
        keywords: [
          "PDF tools",
          "convert PDF",
          "edit PDF",
          "merge PDF",
          "compress PDF"
        ],
        metadataBase: new URL('https://yourdomain.com'),
        openGraph: {
          title: "PDF Tools - All-in-one PDF Solution",
          description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
          url: "https://yourdomain.com",
          siteName: "PDF Tools",
          images: [
            {
              url: "/og-image.jpg",
              width: 1200,
              height: 630,
            }
          ],
          locale: "en_US",
          type: "website",
        },
        twitter: {
          card: "summary_large_image",
          title: "PDF Tools - All-in-one PDF Solution",
          description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
          images: ["/og-image.jpg"],
        },
      };
    }

    // Use the settings from the database
    const metadataBase = settings.siteUrl ? new URL(settings.siteUrl) : new URL('https://yourdomain.com');

    return {
      title: {
        default: settings.metaTitle || "PDF Tools",
        template: `%s | ${settings.siteName || "PDF Tools"}`
      },
      description: settings.metaDescription || "All-in-one PDF solution for your document needs",
      keywords: settings.metaKeywords || ["PDF tools", "convert PDF", "edit PDF"],
      metadataBase,
      openGraph: {
        title: settings.ogTitle || settings.metaTitle || "PDF Tools",
        description: settings.ogDescription || settings.metaDescription || "All-in-one PDF solution",
        url: settings.siteUrl || "https://yourdomain.com",
        siteName: settings.siteName || "PDF Tools",
        images: [
          {
            url: settings.ogImage || "/og-image.jpg",
            width: 1200,
            height: 630,
          }
        ],
        locale: "en_US",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: settings.ogTitle || settings.metaTitle || "PDF Tools",
        description: settings.ogDescription || settings.metaDescription || "All-in-one PDF solution",
        images: [settings.ogImage || "/og-image.jpg"],
        creator: settings.twitterHandle || "@pdftools",
      },
      icons: {
        icon: settings.faviconUrl || "/favicon.ico",
        shortcut: settings.faviconUrl || "/favicon.ico",
        apple: settings.faviconUrl || "/favicon.ico",
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);

    // Return default metadata if there's an error
    return {
      title: {
        default: "PDF Tools - All-in-one PDF Solution",
        template: "%s | PDF Tools"
      },
      description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
      keywords: ["PDF tools", "convert PDF", "edit PDF", "merge PDF", "compress PDF"],
      metadataBase: new URL('https://yourdomain.com'),
      openGraph: {
        title: "PDF Tools - All-in-one PDF Solution",
        description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
        url: "https://yourdomain.com",
        siteName: "PDF Tools",
        images: [{ url: "/og-image.jpg", width: 1200, height: 630 }],
        locale: "en_US",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title: "PDF Tools - All-in-one PDF Solution",
        description: "Free online PDF tools to merge, compress, convert, and edit PDF files",
        images: ["/og-image.jpg"],
      },
    };
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        <link rel="stylesheet" href="/fonts/fonts.css" />
      </head>
      <body>
        {/* Font loading verification script */}
        <Script id="font-verification" strategy="afterInteractive">{`
          // Verify local fonts loaded correctly
          if (typeof window !== 'undefined') {
            // Check if fonts are loaded
            document.fonts.ready.then(() => {
              console.log('All fonts loaded successfully');
            }).catch(err => {
              console.warn('Font loading issue detected, using system fallback');
              document.documentElement.classList.add('font-fallback');
            });
          }
        `}</Script>

        {/* Clean loading state management - NO RETRY LOOPS */}
        <Script id="loading-state-manager" strategy="afterInteractive">
          {`
            // Clean loading state management without retry loops
            if (typeof window !== 'undefined') {
              function resetLoadingStates() {
                // Reset loading meta tag
                let loadingMeta = document.querySelector('meta[name="loading"]');
                if (loadingMeta) {
                  loadingMeta.setAttribute('content', 'false');
                }

                // Hide NProgress bar
                if (typeof NProgress !== 'undefined') {
                  NProgress.done();
                }

                // Reset loading classes
                document.querySelectorAll('.loading, .skeleton').forEach(el => {
                  el.classList.remove('loading', 'skeleton');
                });

                // Clear loading attributes
                document.querySelectorAll('[data-loading="true"]').forEach(el => {
                  el.removeAttribute('data-loading');
                });

                // Dispatch reset event for React components
                const resetEvent = new CustomEvent('reset-loading-states');
                document.dispatchEvent(resetEvent);
              }

              // Reset on page load
              window.addEventListener('load', resetLoadingStates);

              // Reset after route changes
              document.addEventListener('routeChangeComplete', resetLoadingStates);

              // Single fallback timeout
              setTimeout(resetLoadingStates, 5000);
            }
          `}
        </Script>

        <TempoInit />

        <Providers>
          <ThemeInitializer />
          <PlatformInitializer />
          <MetaHead />
          {/* Loading indicator for page transitions */}
          <LoadingIndicator />
          {/* Top loading progress bar */}
          <NavigationProgress />
          {children}
        </Providers>
      </body>
    </html>
  );
}


